# PersistedQueryGraphQLClient with Next.js Fetch Cache

This document demonstrates how to use the `PersistedQueryGraphQLClient` with Next.js fetch cache options.

## Basic Usage

```typescript
import { PersistedQueryGraphQLClient } from "shared-utils"
import persistedDocuments from "./persisted-documents.json"

// Basic client without fetch cache
const client = new PersistedQueryGraphQLClient(
  "https://api.example.com/graphql",
  {
    credentials: "include",
  },
  {
    persistedDocuments: persistedDocuments as Record<string, string>,
    includeFallbackQuery: false,
  }
)
```

## With Next.js Fetch Cache

### Force Cache (Cache Indefinitely)

```typescript
const clientWithForceCache = new PersistedQueryGraphQLClient(
  "https://api.example.com/graphql",
  {
    credentials: "include",
  },
  {
    persistedDocuments: persistedDocuments as Record<string, string>,
    includeFallbackQuery: false,
    fetchCache: {
      cache: "force-cache", // Cache indefinitely
    },
  }
)
```

### No Store (Always Fresh)

```typescript
const clientWithNoStore = new PersistedQueryGraphQLClient(
  "https://api.example.com/graphql",
  {
    credentials: "include",
  },
  {
    persistedDocuments: persistedDocuments as Record<string, string>,
    includeFallbackQuery: false,
    fetchCache: {
      cache: "no-store", // Always fetch fresh data
    },
  }
)
```

### Time-based Revalidation

```typescript
const clientWithRevalidation = new PersistedQueryGraphQLClient(
  "https://api.example.com/graphql",
  {
    credentials: "include",
  },
  {
    persistedDocuments: persistedDocuments as Record<string, string>,
    includeFallbackQuery: false,
    fetchCache: {
      cache: "force-cache",
      revalidate: 3600, // Revalidate every hour (3600 seconds)
    },
  }
)
```

### Tag-based Revalidation

```typescript
const clientWithTags = new PersistedQueryGraphQLClient(
  "https://api.example.com/graphql",
  {
    credentials: "include",
  },
  {
    persistedDocuments: persistedDocuments as Record<string, string>,
    includeFallbackQuery: false,
    fetchCache: {
      cache: "force-cache",
      tags: ["products", "user-data"], // Tags for on-demand revalidation
    },
  }
)

// Later, you can revalidate using:
// revalidateTag("products")
```

### Combined Configuration

```typescript
const clientWithFullConfig = new PersistedQueryGraphQLClient(
  "https://api.example.com/graphql",
  {
    credentials: "include",
  },
  {
    persistedDocuments: persistedDocuments as Record<string, string>,
    includeFallbackQuery: process.env.NODE_ENV === "development",
    fetchCache: {
      cache: "force-cache",
      revalidate: 1800, // 30 minutes
      tags: ["graphql", "api-data"],
    },
  }
)
```

## Usage in Server Components

```typescript
// app/products/page.tsx
import { clientWithRevalidation } from "@/utils/graphqlClient"
import { GetProductsDocument } from "@/generated/graphql"

export default async function ProductsPage() {
  // This will use Next.js fetch cache with the configured options
  const data = await clientWithRevalidation.request(GetProductsDocument)
  
  return (
    <div>
      {data.products.map((product) => (
        <div key={product.id}>{product.name}</div>
      ))}
    </div>
  )
}
```

## Cache Options Reference

### `cache`
- `"auto no cache"` (default): Next.js default behavior
- `"no-store"`: Always fetch fresh data
- `"force-cache"`: Use cache if available, fetch and cache if not

### `revalidate`
- `false`: Cache indefinitely
- `0`: Prevent caching
- `number`: Cache lifetime in seconds

### `tags`
- Array of strings for on-demand revalidation
- Max 256 characters per tag, max 128 tags
- Use with `revalidateTag()` function

## Environment-specific Configuration

```typescript
const client = new PersistedQueryGraphQLClient(
  GRAPHQL_URL,
  requestConfig,
  {
    persistedDocuments,
    includeFallbackQuery: IS_DEVELOPMENT || IS_STAGING,
    fetchCache: IS_PRODUCTION ? {
      cache: "force-cache",
      revalidate: 3600,
      tags: ["production-data"],
    } : {
      cache: "no-store", // Always fresh in development
    },
  }
)
```
