import { type TypedDocumentNode } from "@graphql-typed-document-node/core"
import { print } from "graphql"
import {
  GraphQLClient,
  type GraphQLResponse,
  type RequestDocument,
  type Variables,
  type RequestOptions,
} from "graphql-request"
import {
  type VariablesAndRequestHeadersArgs,
  type RequestConfig,
} from "graphql-request/build/legacy/helpers/types"

/**
 * Next.js fetch cache configuration
 */
export interface NextJSFetchCacheConfig {
  /**
   * Configure how the request should interact with Next.js Data Cache
   * @default 'auto no cache'
   */
  cache?: "auto no cache" | "no-store" | "force-cache"
  /**
   * Set the cache lifetime of a resource (in seconds)
   * - false: Cache indefinitely
   * - 0: Prevent caching
   * - number: Cache for specified seconds
   */
  revalidate?: false | 0 | number
  /**
   * Set the cache tags of a resource for on-demand revalidation
   * Max length per tag: 256 characters, max tags: 128
   */
  tags?: string[]
}

/**
 * Configuration for persisted query client wrapper
 */
export interface PersistedQueryGraphQLClientConfig {
  /**
   * The persisted documents manifest (query hash -> query string mapping)
   */
  persistedDocuments: Record<string, string>
  /**
   * Whether to include the full query as fallback (for development)
   * @default false
   */
  includeFallbackQuery?: boolean
  /**
   * Next.js fetch cache configuration
   * When enabled, applies Next.js caching to GraphQL requests
   */
  fetchCache?: NextJSFetchCacheConfig
}

/**
 * Check if the response contains a "PersistedQueryNotFound" error
 */
function isPersistedQueryNotFoundError(response: unknown): boolean {
  try {
    const responseObj = response as GraphQLResponse
    if (responseObj?.errors) {
      return responseObj.errors.some(
        (error) =>
          error.extensions?.code === "PERSISTED_QUERY_NOT_FOUND" ||
          error.message?.includes("PersistedQueryNotFound") ||
          error.message?.includes("persisted query not found")
      )
    }
    return false
  } catch {
    return false
  }
}

/**
 * Find the persisted document ID for a given query
 */
function findPersistedDocumentId(
  query: string,
  persistedDocuments: Record<string, string>
): string | undefined {
  // Normalize the query by removing extra whitespace for comparison
  const normalizedQuery = query.replace(/\s+/g, " ").trim()

  // Look for the query in the persisted documents manifest
  for (const [id, persistedQuery] of Object.entries(persistedDocuments)) {
    const normalizedPersistedQuery = persistedQuery.replace(/\s+/g, " ").trim()
    if (normalizedQuery === normalizedPersistedQuery) {
      return id
    }
  }

  return undefined
}

/**
 * Create a persisted query document with hash
 */
function createPersistedQueryDocument<T, V>(persistedDocumentId: string) {
  return {
    extensions: {
      persistedQuery: {
        version: 1,
        sha256Hash: persistedDocumentId,
      },
    },
  } as unknown as TypedDocumentNode<T, V>
}

/**
 * GraphQL client that extends the base GraphQLClient with persisted query support
 */
export default class PersistedQueryGraphQLClient extends GraphQLClient {
  private config: PersistedQueryGraphQLClientConfig

  constructor(
    url: string,
    requestConfig?: RequestConfig,
    config?: PersistedQueryGraphQLClientConfig
  ) {
    super(url, requestConfig)
    this.config = config ?? { persistedDocuments: {} }
  }

  /**
   * Extract query string from document
   */
  private extractQueryString<T, V>(
    document: RequestDocument | TypedDocumentNode<T, V>
  ): string {
    return typeof document === "string" ? document : print(document)
  }

  /**
   * Apply Next.js fetch cache options to request options
   */
  private applyFetchCacheOptions<V extends Variables, T>(
    options: RequestOptions<V, T>
  ): RequestOptions<V, T> {
    if (!this.config.fetchCache) {
      return options
    }

    const { cache, revalidate, tags } = this.config.fetchCache
    const fetchOptions: RequestInit = {}

    // Apply cache option (handle Next.js specific 'auto no cache' option)
    if (cache && cache !== "auto no cache") {
      // Only apply standard RequestCache values, skip Next.js specific 'auto no cache'
      fetchOptions.cache = cache as RequestCache
    }

    // Apply Next.js specific options
    const nextOptions: { revalidate?: false | 0 | number; tags?: string[] } = {}
    if (revalidate !== undefined) {
      nextOptions.revalidate = revalidate
    }
    if (tags && tags.length > 0) {
      nextOptions.tags = tags
    }

    // Merge with existing fetch options
    const updatedOptions: RequestOptions<V, T> = {
      ...options,
      fetch: {
        ...fetchOptions,
        ...(Object.keys(nextOptions).length > 0 && { next: nextOptions }),
      },
    }

    return updatedOptions
  }

  /**
   * Execute persisted query request with retry logic using options signature
   */
  private async executePersistedQueryWithOptions<
    T,
    V extends Variables = Variables
  >(options: RequestOptions<V, T>, queryString: string): Promise<T> {
    // Apply Next.js fetch cache options
    const optionsWithCache = this.applyFetchCacheOptions(options)

    // If includeFallbackQuery is enabled, send full query immediately
    if (this.config.includeFallbackQuery) {
      return super.request<T, V>(optionsWithCache)
    }

    // Try to find the persisted document ID
    const persistedDocumentId = findPersistedDocumentId(
      queryString,
      this.config.persistedDocuments
    )

    // If no persisted document found, fall back to normal query
    if (!persistedDocumentId) {
      return super.request<T, V>(optionsWithCache)
    }

    try {
      // First attempt: send only the persisted query hash
      const persistedQueryDocument = createPersistedQueryDocument<T, V>(
        persistedDocumentId
      )

      const persistedOptions: RequestOptions<V, T> = {
        ...optionsWithCache,
        document: persistedQueryDocument,
      }

      return await super.request<T, V>(persistedOptions)
    } catch (error) {
      // Check if it's a persisted query not found error
      if (isPersistedQueryNotFoundError(error)) {
        // Retry with the full query
        return super.request<T, V>(optionsWithCache)
      }

      // Re-throw other errors
      throw error
    }
  }

  /**
   * Request method with persisted query support
   * Supports both document with variables/headers signature and options signature
   */
  async request<T, V extends Variables = Variables>(
    document: RequestDocument | TypedDocumentNode<T, V>,
    ...variablesAndRequestHeaders: VariablesAndRequestHeadersArgs<V>
  ): Promise<T>
  async request<T, V extends Variables = Variables>(
    options: RequestOptions<V, T>
  ): Promise<T>
  async request<T, V extends Variables = Variables>(
    documentOrOptions:
      | RequestDocument
      | TypedDocumentNode<T, V>
      | RequestOptions<V, T>,
    ...variablesAndRequestHeaders: VariablesAndRequestHeadersArgs<V>
  ): Promise<T> {
    // Check if first argument is options object
    if (
      typeof documentOrOptions === "object" &&
      documentOrOptions !== null &&
      "document" in documentOrOptions
    ) {
      // Handle options signature
      const options = documentOrOptions as RequestOptions<V, T>
      const queryString = this.extractQueryString(options.document)
      return this.executePersistedQueryWithOptions<T, V>(options, queryString)
    } else {
      // Handle document with variables/headers signature
      const document = documentOrOptions as
        | RequestDocument
        | TypedDocumentNode<T, V>
      const queryString = this.extractQueryString(document)
      const options = {
        document,
        variables: variablesAndRequestHeaders?.[0],
        requestHeaders: variablesAndRequestHeaders?.[1],
      } as RequestOptions<V, T>

      return this.executePersistedQueryWithOptions<T, V>(options, queryString)
    }
  }
}
